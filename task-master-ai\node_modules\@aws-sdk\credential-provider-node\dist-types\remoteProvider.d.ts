import type { FromHttpOptions } from "@aws-sdk/credential-provider-http";
import type { RemoteProviderInit } from "@smithy/credential-provider-imds";
import type { AwsCredentialIdentityProvider } from "@smithy/types";
/**
 * @internal
 */
export declare const ENV_IMDS_DISABLED = "AWS_EC2_METADATA_DISABLED";
/**
 * @internal
 */
export declare const remoteProvider: (init: RemoteProviderInit | FromHttpOptions) => Promise<AwsCredentialIdentityProvider>;
