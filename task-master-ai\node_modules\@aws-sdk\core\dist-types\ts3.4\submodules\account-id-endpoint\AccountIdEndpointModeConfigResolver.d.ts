import { Provider } from "@smithy/types";
import { AccountIdEndpointMode } from "./AccountIdEndpointModeConstants";
export interface AccountIdEndpointModeInputConfig {
  accountIdEndpointMode?:
    | AccountIdEndpointMode
    | Provider<AccountIdEndpointMode>;
}
interface PreviouslyResolved {}
export interface AccountIdEndpointModeResolvedConfig {
  accountIdEndpointMode: Provider<AccountIdEndpointMode>;
}
export declare const resolveAccountIdEndpointModeConfig: <T>(
  input: T & AccountIdEndpointModeInputConfig & PreviouslyResolved
) => T & AccountIdEndpointModeResolvedConfig;
export {};
