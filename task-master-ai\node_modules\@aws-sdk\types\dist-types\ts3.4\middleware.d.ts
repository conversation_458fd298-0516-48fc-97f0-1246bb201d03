import { HandlerExecutionContext } from "@smithy/types";
import { AwsSdkFeatures } from "./feature-ids";
export {
  AbsoluteLocation,
  BuildHandler,
  BuildHandlerArguments,
  BuildHandlerOptions,
  BuildHandlerOutput,
  BuildMiddleware,
  DeserializeHandler,
  DeserializeHandlerArguments,
  DeserializeHandlerOptions,
  DeserializeHandlerOutput,
  DeserializeMiddleware,
  FinalizeHandler,
  FinalizeHandlerArguments,
  FinalizeHandlerOutput,
  FinalizeRequestHandlerOptions,
  FinalizeRequestMiddleware,
  Handler,
  HandlerExecutionContext,
  HandlerOptions,
  InitializeHandler,
  InitializeHandlerArguments,
  InitializeHandlerOptions,
  InitializeHandlerOutput,
  InitializeMiddleware,
  MiddlewareStack,
  MiddlewareType,
  Pluggable,
  Priority,
  Relation,
  RelativeLocation,
  RelativeMiddlewareOptions,
  SerializeHandler,
  SerializeHandlerArguments,
  SerializeHandlerOptions,
  SerializeHandlerOutput,
  SerializeMiddleware,
  Step,
  Terminalware,
} from "@smithy/types";
export interface AwsHandlerExecutionContext extends HandlerExecutionContext {
  __aws_sdk_context?: {
    features?: AwsSdkFeatures;
  };
}
