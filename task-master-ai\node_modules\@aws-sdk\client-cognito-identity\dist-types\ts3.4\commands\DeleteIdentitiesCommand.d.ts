import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  CognitoIdentityClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../CognitoIdentityClient";
import {
  DeleteIdentitiesInput,
  DeleteIdentitiesResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DeleteIdentitiesCommandInput extends DeleteIdentitiesInput {}
export interface DeleteIdentitiesCommandOutput
  extends DeleteIdentitiesResponse,
    __MetadataBearer {}
declare const DeleteIdentitiesCommand_base: {
  new (
    input: DeleteIdentitiesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteIdentitiesCommandInput,
    DeleteIdentitiesCommandOutput,
    CognitoIdentityClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteIdentitiesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteIdentitiesCommandInput,
    DeleteIdentitiesCommandOutput,
    CognitoIdentityClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteIdentitiesCommand extends DeleteIdentitiesCommand_base {
  protected static __types: {
    api: {
      input: DeleteIdentitiesInput;
      output: DeleteIdentitiesResponse;
    };
    sdk: {
      input: DeleteIdentitiesCommandInput;
      output: DeleteIdentitiesCommandOutput;
    };
  };
}
