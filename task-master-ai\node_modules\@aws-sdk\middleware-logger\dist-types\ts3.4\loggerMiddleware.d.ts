import {
  AbsoluteLocation,
  HandlerExecutionContext,
  InitializeHandler,
  InitializeHandlerOptions,
  MetadataBearer,
  Pluggable,
} from "@smithy/types";
export declare const loggerMiddleware: () => <
  Output extends MetadataBearer = MetadataBearer
>(
  next: InitializeHandler<any, Output>,
  context: HandlerExecutionContext
) => InitializeHandler<any, Output>;
export declare const loggerMiddlewareOptions: InitializeHandlerOptions &
  AbsoluteLocation;
export declare const getLoggerPlugin: (options: any) => Pluggable<any, any>;
