import { HttpRequest as __HttpRequest, HttpResponse as __HttpResponse } from "@smithy/protocol-http";
import { SerdeContext as __SerdeContext } from "@smithy/types";
import { CreateIdentityPoolCommandInput, CreateIdentityPoolCommandOutput } from "../commands/CreateIdentityPoolCommand";
import { DeleteIdentitiesCommandInput, DeleteIdentitiesCommandOutput } from "../commands/DeleteIdentitiesCommand";
import { DeleteIdentityPoolCommandInput, DeleteIdentityPoolCommandOutput } from "../commands/DeleteIdentityPoolCommand";
import { DescribeIdentityCommandInput, DescribeIdentityCommandOutput } from "../commands/DescribeIdentityCommand";
import { DescribeIdentityPoolCommandInput, DescribeIdentityPoolCommandOutput } from "../commands/DescribeIdentityPoolCommand";
import { GetCredentialsForIdentityCommandInput, GetCredentialsForIdentityCommandOutput } from "../commands/GetCredentialsForIdentityCommand";
import { GetIdCommandInput, GetIdCommandOutput } from "../commands/GetIdCommand";
import { GetIdentityPoolRolesCommandInput, GetIdentityPoolRolesCommandOutput } from "../commands/GetIdentityPoolRolesCommand";
import { GetOpenIdTokenCommandInput, GetOpenIdTokenCommandOutput } from "../commands/GetOpenIdTokenCommand";
import { GetOpenIdTokenForDeveloperIdentityCommandInput, GetOpenIdTokenForDeveloperIdentityCommandOutput } from "../commands/GetOpenIdTokenForDeveloperIdentityCommand";
import { GetPrincipalTagAttributeMapCommandInput, GetPrincipalTagAttributeMapCommandOutput } from "../commands/GetPrincipalTagAttributeMapCommand";
import { ListIdentitiesCommandInput, ListIdentitiesCommandOutput } from "../commands/ListIdentitiesCommand";
import { ListIdentityPoolsCommandInput, ListIdentityPoolsCommandOutput } from "../commands/ListIdentityPoolsCommand";
import { ListTagsForResourceCommandInput, ListTagsForResourceCommandOutput } from "../commands/ListTagsForResourceCommand";
import { LookupDeveloperIdentityCommandInput, LookupDeveloperIdentityCommandOutput } from "../commands/LookupDeveloperIdentityCommand";
import { MergeDeveloperIdentitiesCommandInput, MergeDeveloperIdentitiesCommandOutput } from "../commands/MergeDeveloperIdentitiesCommand";
import { SetIdentityPoolRolesCommandInput, SetIdentityPoolRolesCommandOutput } from "../commands/SetIdentityPoolRolesCommand";
import { SetPrincipalTagAttributeMapCommandInput, SetPrincipalTagAttributeMapCommandOutput } from "../commands/SetPrincipalTagAttributeMapCommand";
import { TagResourceCommandInput, TagResourceCommandOutput } from "../commands/TagResourceCommand";
import { UnlinkDeveloperIdentityCommandInput, UnlinkDeveloperIdentityCommandOutput } from "../commands/UnlinkDeveloperIdentityCommand";
import { UnlinkIdentityCommandInput, UnlinkIdentityCommandOutput } from "../commands/UnlinkIdentityCommand";
import { UntagResourceCommandInput, UntagResourceCommandOutput } from "../commands/UntagResourceCommand";
import { UpdateIdentityPoolCommandInput, UpdateIdentityPoolCommandOutput } from "../commands/UpdateIdentityPoolCommand";
/**
 * serializeAws_json1_1CreateIdentityPoolCommand
 */
export declare const se_CreateIdentityPoolCommand: (input: CreateIdentityPoolCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteIdentitiesCommand
 */
export declare const se_DeleteIdentitiesCommand: (input: DeleteIdentitiesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DeleteIdentityPoolCommand
 */
export declare const se_DeleteIdentityPoolCommand: (input: DeleteIdentityPoolCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeIdentityCommand
 */
export declare const se_DescribeIdentityCommand: (input: DescribeIdentityCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1DescribeIdentityPoolCommand
 */
export declare const se_DescribeIdentityPoolCommand: (input: DescribeIdentityPoolCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1GetCredentialsForIdentityCommand
 */
export declare const se_GetCredentialsForIdentityCommand: (input: GetCredentialsForIdentityCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1GetIdCommand
 */
export declare const se_GetIdCommand: (input: GetIdCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1GetIdentityPoolRolesCommand
 */
export declare const se_GetIdentityPoolRolesCommand: (input: GetIdentityPoolRolesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1GetOpenIdTokenCommand
 */
export declare const se_GetOpenIdTokenCommand: (input: GetOpenIdTokenCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1GetOpenIdTokenForDeveloperIdentityCommand
 */
export declare const se_GetOpenIdTokenForDeveloperIdentityCommand: (input: GetOpenIdTokenForDeveloperIdentityCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1GetPrincipalTagAttributeMapCommand
 */
export declare const se_GetPrincipalTagAttributeMapCommand: (input: GetPrincipalTagAttributeMapCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListIdentitiesCommand
 */
export declare const se_ListIdentitiesCommand: (input: ListIdentitiesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListIdentityPoolsCommand
 */
export declare const se_ListIdentityPoolsCommand: (input: ListIdentityPoolsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1ListTagsForResourceCommand
 */
export declare const se_ListTagsForResourceCommand: (input: ListTagsForResourceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1LookupDeveloperIdentityCommand
 */
export declare const se_LookupDeveloperIdentityCommand: (input: LookupDeveloperIdentityCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1MergeDeveloperIdentitiesCommand
 */
export declare const se_MergeDeveloperIdentitiesCommand: (input: MergeDeveloperIdentitiesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1SetIdentityPoolRolesCommand
 */
export declare const se_SetIdentityPoolRolesCommand: (input: SetIdentityPoolRolesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1SetPrincipalTagAttributeMapCommand
 */
export declare const se_SetPrincipalTagAttributeMapCommand: (input: SetPrincipalTagAttributeMapCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1TagResourceCommand
 */
export declare const se_TagResourceCommand: (input: TagResourceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UnlinkDeveloperIdentityCommand
 */
export declare const se_UnlinkDeveloperIdentityCommand: (input: UnlinkDeveloperIdentityCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UnlinkIdentityCommand
 */
export declare const se_UnlinkIdentityCommand: (input: UnlinkIdentityCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UntagResourceCommand
 */
export declare const se_UntagResourceCommand: (input: UntagResourceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_json1_1UpdateIdentityPoolCommand
 */
export declare const se_UpdateIdentityPoolCommand: (input: UpdateIdentityPoolCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * deserializeAws_json1_1CreateIdentityPoolCommand
 */
export declare const de_CreateIdentityPoolCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateIdentityPoolCommandOutput>;
/**
 * deserializeAws_json1_1DeleteIdentitiesCommand
 */
export declare const de_DeleteIdentitiesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteIdentitiesCommandOutput>;
/**
 * deserializeAws_json1_1DeleteIdentityPoolCommand
 */
export declare const de_DeleteIdentityPoolCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteIdentityPoolCommandOutput>;
/**
 * deserializeAws_json1_1DescribeIdentityCommand
 */
export declare const de_DescribeIdentityCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeIdentityCommandOutput>;
/**
 * deserializeAws_json1_1DescribeIdentityPoolCommand
 */
export declare const de_DescribeIdentityPoolCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeIdentityPoolCommandOutput>;
/**
 * deserializeAws_json1_1GetCredentialsForIdentityCommand
 */
export declare const de_GetCredentialsForIdentityCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetCredentialsForIdentityCommandOutput>;
/**
 * deserializeAws_json1_1GetIdCommand
 */
export declare const de_GetIdCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetIdCommandOutput>;
/**
 * deserializeAws_json1_1GetIdentityPoolRolesCommand
 */
export declare const de_GetIdentityPoolRolesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetIdentityPoolRolesCommandOutput>;
/**
 * deserializeAws_json1_1GetOpenIdTokenCommand
 */
export declare const de_GetOpenIdTokenCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetOpenIdTokenCommandOutput>;
/**
 * deserializeAws_json1_1GetOpenIdTokenForDeveloperIdentityCommand
 */
export declare const de_GetOpenIdTokenForDeveloperIdentityCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetOpenIdTokenForDeveloperIdentityCommandOutput>;
/**
 * deserializeAws_json1_1GetPrincipalTagAttributeMapCommand
 */
export declare const de_GetPrincipalTagAttributeMapCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetPrincipalTagAttributeMapCommandOutput>;
/**
 * deserializeAws_json1_1ListIdentitiesCommand
 */
export declare const de_ListIdentitiesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListIdentitiesCommandOutput>;
/**
 * deserializeAws_json1_1ListIdentityPoolsCommand
 */
export declare const de_ListIdentityPoolsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListIdentityPoolsCommandOutput>;
/**
 * deserializeAws_json1_1ListTagsForResourceCommand
 */
export declare const de_ListTagsForResourceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListTagsForResourceCommandOutput>;
/**
 * deserializeAws_json1_1LookupDeveloperIdentityCommand
 */
export declare const de_LookupDeveloperIdentityCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<LookupDeveloperIdentityCommandOutput>;
/**
 * deserializeAws_json1_1MergeDeveloperIdentitiesCommand
 */
export declare const de_MergeDeveloperIdentitiesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<MergeDeveloperIdentitiesCommandOutput>;
/**
 * deserializeAws_json1_1SetIdentityPoolRolesCommand
 */
export declare const de_SetIdentityPoolRolesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<SetIdentityPoolRolesCommandOutput>;
/**
 * deserializeAws_json1_1SetPrincipalTagAttributeMapCommand
 */
export declare const de_SetPrincipalTagAttributeMapCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<SetPrincipalTagAttributeMapCommandOutput>;
/**
 * deserializeAws_json1_1TagResourceCommand
 */
export declare const de_TagResourceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<TagResourceCommandOutput>;
/**
 * deserializeAws_json1_1UnlinkDeveloperIdentityCommand
 */
export declare const de_UnlinkDeveloperIdentityCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UnlinkDeveloperIdentityCommandOutput>;
/**
 * deserializeAws_json1_1UnlinkIdentityCommand
 */
export declare const de_UnlinkIdentityCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UnlinkIdentityCommandOutput>;
/**
 * deserializeAws_json1_1UntagResourceCommand
 */
export declare const de_UntagResourceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UntagResourceCommandOutput>;
/**
 * deserializeAws_json1_1UpdateIdentityPoolCommand
 */
export declare const de_UpdateIdentityPoolCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateIdentityPoolCommandOutput>;
