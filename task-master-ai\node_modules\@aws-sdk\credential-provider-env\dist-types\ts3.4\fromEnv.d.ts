import { CredentialProviderOptions } from "@aws-sdk/types";
import { AwsCredentialIdentityProvider } from "@smithy/types";
export interface FromEnvInit extends CredentialProviderOptions {}
export declare const ENV_KEY = "AWS_ACCESS_KEY_ID";
export declare const ENV_SECRET = "AWS_SECRET_ACCESS_KEY";
export declare const ENV_SESSION = "AWS_SESSION_TOKEN";
export declare const ENV_EXPIRATION = "AWS_CREDENTIAL_EXPIRATION";
export declare const ENV_CREDENTIAL_SCOPE = "AWS_CREDENTIAL_SCOPE";
export declare const ENV_ACCOUNT_ID = "AWS_ACCOUNT_ID";
export declare const fromEnv: (
  init?: FromEnvInit
) => AwsCredentialIdentityProvider;
